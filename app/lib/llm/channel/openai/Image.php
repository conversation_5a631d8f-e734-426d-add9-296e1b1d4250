<?php

namespace app\lib\llm\channel\openai;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return match ($model) {
            'gpt-image-1' => [
                'sizes' => ['1024x1024', '1536x1024', '1024x1536',],
            ],
            default => [
                'sizes'  => ['1024x1024', '1792x1024', '1024x1792'],
                'styles' => [
                    [
                        'name' => '生动',
                        'code' => null,
                    ],
                    [
                        'name' => '自然',
                        'code' => 'natural',
                    ],
                ],
            ]
        };
    }

    protected function transformData(&$data, $model, $params)
    {
        switch ($model) {
            case 'gpt-image-1':
                break;
            default:
                $data['quality']         = $params['quality'] ?? 'standard';
                $data['style']           = $params['style'] ?? 'vivid';
                $data['response_format'] = $params['response_format'] ?? 'url';
                break;
        }
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt  = $params['prompt'] ?? '';
        $size    = $params['size'] ?? '1024x1024';
        $quality = $params['quality'] ?? 'standard';

        $data = [
            'model'  => $model,
            'prompt' => $prompt,
            'size'   => $size,
        ];

        $this->transformData($data, $model, $params);

        $result = $this->request('/v1/images/generations', [
            'json' => $data,
        ]);

        $usage = 1;
        if (in_array($size, ['1024x1792', '1792x1024'])) {
            $usage += 1;
        }
        if ($quality == 'hd') {
            $usage += 1;
        }

        return [
            'images' => $result['data'],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 0,
                'completion_tokens' => $usage,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function edit($params)
    {
        $result = $this->request('/v1/images/edits', [
            'json' => [
                'model'           => $this->options['checkpoint'] ?? null,
                'prompt'          => $params['prompt'] ?? '',
                'size'            => 'auto',
                'response_format' => $params['response_format'] ?? 'url',
            ],
        ]);

        return [
            'images' => $result['data'],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 0,
                'completion_tokens' => 1,
                'total_tokens'      => 1,
            ]),
        ];
    }
}
