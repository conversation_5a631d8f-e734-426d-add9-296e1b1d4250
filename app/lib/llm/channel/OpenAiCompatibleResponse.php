<?php

namespace app\lib\llm\channel;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Str;

trait OpenAiCompatibleResponse
{
    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);
            if ($result) {
                throw new Exception($result['error']['message']);
            }
            throw new Exception('Unknown error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        if (!Str::contains($contentType, 'json')) {
            return $response;
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
