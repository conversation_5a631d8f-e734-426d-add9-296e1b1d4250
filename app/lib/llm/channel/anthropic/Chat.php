<?php

namespace app\lib\llm\channel\anthropic;

use Psr\Http\Message\StreamInterface;
use think\helper\Arr;

class Chat extends Driver
{
    public function completions($params)
    {
        $model     = $this->options['checkpoint'] ?? null;
        $reasoning = $this->options['params']['reasoning'] ?? false;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? null;
        $stream      = $params['stream'] ?? true;
        $moderation  = $params['moderation'] ?? false;
        $maxTokens   = $params['max_tokens'] ?? 8192;
        $temperature = $params['temperature'] ?? null;

        if ($reasoning) {
            $thinking    = [
                'budget_tokens' => 8192,
                'type'          => 'enabled',
            ];
            $maxTokens   *= 2;
            $temperature = 1;
        } else {
            $thinking = [
                'type' => 'disabled',
            ];
        }

        $extra = 0;
        if ($moderation) {
            $extra = $this->moderation($messages);
        }

        $system      = null;
        $toolContent = null;
        $index       = 0;

        $messages = Arr::flatMap(function ($item) use (&$system, &$toolContent, &$index) {
            $role = Arr::get($item, 'role');

            if ($role != 'tool') {
                ++$index;
            }
            switch (true) {
                case $role == 'system':
                    $system = Arr::get($item, 'content');
                    return [];
                case $role == 'assistant' and !empty($item['tool_calls']):
                    $content = Arr::flatMap(function ($call) {
                        $function = Arr::get($call, 'function');
                        if (empty($function)) {
                            return [];
                        }
                        $tool = [
                            'type'  => 'tool_use',
                            'id'    => $call['id'],
                            'name'  => $function['name'],
                            'input' => (object) json_decode($function['arguments'] ?? ''),
                        ];
                        return [$tool];
                    }, $item['tool_calls']);

                    if (!empty($item['content'])) {
                        array_unshift($content, [
                            'type' => 'text',
                            'text' => $item['content'],
                        ]);
                    }

                    $item = [
                        'role'    => 'assistant',
                        'content' => $content,
                    ];
                    break;
                case $role == 'tool':
                    if (empty($toolContent[$index])) {
                        $toolContent[$index] = [
                            [
                                'type'        => 'tool_result',
                                'tool_use_id' => Arr::get($item, 'tool_call_id'),
                                'content'     => Arr::get($item, 'content'),
                            ],
                        ];
                        $item                = [
                            'role'    => 'user',
                            'content' => &$toolContent[$index],
                        ];
                    } else {
                        $toolContent[$index][] = [
                            'type'        => 'tool_result',
                            'tool_use_id' => Arr::get($item, 'tool_call_id'),
                            'content'     => Arr::get($item, 'content'),
                        ];
                        return [];
                    }
                    break;
            }
            return [$item];
        }, $messages);

        if (!empty($tools)) {
            $tools = Arr::flatMap(function ($tool) {
                if (Arr::get($tool, 'type') == 'function') {
                    $function = Arr::get($tool, 'function', []);
                    return [
                        [
                            'name'         => $function['name'],
                            'input_schema' => $function['parameters'] ?? [
                                    'type' => 'object',
                                ],
                            'description'  => $function['description'] ?? null,
                        ],
                    ];
                }
                return [];
            }, $tools);
        }

        $res = $this->request('/v1/messages', [
            'json'   => array_filter_null([
                'model'       => $model,
                'system'      => $system,
                'messages'    => $messages,
                'stream'      => $stream,
                'tools'       => $tools,
                'temperature' => $temperature,
                'max_tokens'  => $maxTokens,
                'thinking'    => $thinking,
            ]),
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $finishReason = null;
            $blockType    = null;
            $usage        = [];

            foreach ($this->getMessages($res) as $message) {
                $data   = $message['data'];
                $result = json_decode($data, true);
                $type   = Arr::get($result, 'type');

                switch ($type) {
                    case 'message_start':
                        $usage['prompt_tokens'] = Arr::get($result, 'message.usage.input_tokens', 0);
                        break;
                    case 'message_delta':
                        $usage['completion_tokens'] = Arr::get($result, 'usage.output_tokens', 0);
                        $finishReason               = Arr::get($result, 'delta.stop_reason');
                        break;
                    case 'message_stop':
                        $finishReason = $this->getFinishReason($finishReason);
                        yield [
                            'usage'         => $this->applyFactor($usage),
                            'finish_reason' => $finishReason,
                        ];
                        break;
                    case 'content_block_start':
                        $blockType = Arr::get($result, 'content_block.type');
                        if ($blockType == 'tool_use') {
                            $call = [
                                'id'       => Arr::get($result, 'content_block.id'),
                                'type'     => 'function',
                                'function' => [
                                    'name'      => Arr::get($result, 'content_block.name'),
                                    'arguments' => '',
                                ],
                            ];
                        }
                        break;
                    case 'content_block_delta':
                        switch ($blockType) {
                            case 'thinking':
                                if ($result['delta']['type'] == 'thinking_delta') {
                                    yield [
                                        'delta'         => [
                                            'role'      => 'assistant',
                                            'reasoning' => $result['delta']['thinking'],
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                                break;
                            case 'text':
                                if ($result['delta']['type'] == 'text_delta') {
                                    yield [
                                        'delta'         => [
                                            'role'    => 'assistant',
                                            'content' => $result['delta']['text'],
                                        ],
                                        'finish_reason' => null,
                                    ];
                                }
                                break;
                            case 'tool_use':
                                if ($result['delta']['type'] == 'input_json_delta') {
                                    $call['function']['arguments'] .= $result['delta']['partial_json'];
                                }
                                break;
                        }

                        break;
                    case 'content_block_stop':
                        if ($blockType == 'tool_use') {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = null;
                        }
                        $blockType = null;
                        break;
                }
            }
        } else {
            $content   = '';
            $reasoning = '';
            $toolCalls = [];

            foreach ($res['content'] as $item) {
                switch ($item['type']) {
                    case 'thinking':
                        $reasoning .= $item['thinking'];
                        break;
                    case 'text':
                        $content .= $item['text'];
                        break;
                    case 'tool_use':
                        $toolCalls[] = [
                            'id'       => $item['id'],
                            'type'     => 'function',
                            'function' => [
                                'name'      => $item['name'],
                                'arguments' => json_encode($item['input']),
                            ],
                        ];
                        break;
                }
            }

            $message = [
                'role'    => 'assistant',
                'content' => $content,
            ];

            if (!empty($reasoning)) {
                $message['reasoning'] = $reasoning;
            }

            if (!empty($toolCalls)) {
                $message['tool_calls'] = $toolCalls;
            }

            $finishReason = $this->getFinishReason($res['stop_reason']);
            $usage        = [
                'prompt_tokens'     => $res['usage']['input_tokens'],
                'completion_tokens' => $res['usage']['output_tokens'],
            ];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage, $extra),
            ];
        }
    }

    protected function getFinishReason($text)
    {
        return match ($text) {
            'end_turn' => 'stop',
            'tool_use' => 'tool_calls',
            default => $text,
        };
    }
}
