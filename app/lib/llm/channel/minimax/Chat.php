<?php

namespace app\lib\llm\channel\minimax;

use app\lib\llm\contract\ChatInterface;
use app\lib\llm\Exception;
use Psr\Http\Message\StreamInterface;
use think\helper\Arr;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? null;

        $temperature = $temperature === 0 ? 0.01 : $temperature;

        $tools = array_map(function ($tool) {
            if (isset($tool['function'])) {
                $tool['function']['parameters'] = json_encode($tool['function']['parameters'] ?? []);
            }
            return $tool;
        }, $tools);

        $res = $this->request('text/chatcompletion_v2', [
            'json'   => [
                'model'       => $model,
                'messages'    => $messages,
                'stream'      => $stream,
                'tools'       => $tools,
                'temperature' => $temperature,
                'max_tokens'  => $params['max_tokens'] ?? 4096,
            ],
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];

                $result = json_decode($data, true);

                $resp = Arr::get($result, 'base_resp');

                if ($resp) {
                    $code = Arr::get($resp, 'status_code', 0);
                    if ($code != 0) {
                        throw new Exception(Arr::get($resp, 'status_msg', 'Unknown error'));
                    }

                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }

                    if (!empty($result['usage'])) {
                        yield [
                            'usage'         => $this->applyFactor([
                                'prompt_tokens'     => 0,
                                'completion_tokens' => $result['usage']['total_tokens'],
                                'total_tokens'      => $result['usage']['total_tokens'],
                            ]),
                            'finish_reason' => $result['choices'][0]['finish_reason'] ?? null,
                        ];
                        break;
                    }
                } else {
                    $delta = $result['choices'][0]['delta'] ?? null;
                    if (!empty($delta)) {
                        //检查是否为tools_call
                        if (!empty($delta['tool_calls'])) {
                            $current = $delta['tool_calls'][0];
                            if ($call) {
                                $call['function']['name']      .= $current['function']['name'];
                                $call['function']['arguments'] .= $current['function']['arguments'];
                            } else {
                                $call['index'] = 0;

                                $call = $current;
                            }
                        } else {
                            yield [
                                'delta'         => $delta,
                                'finish_reason' => null,
                            ];
                        }
                    }
                }
            }
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor([
                    'prompt_tokens'     => 0,
                    'completion_tokens' => $usage['total_tokens'],
                    'total_tokens'      => $usage['total_tokens'],
                ]),
            ];
        }
    }
}
