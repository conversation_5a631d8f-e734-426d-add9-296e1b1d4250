<?php

namespace app\lib\llm\channel\topthink;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages = $params['messages'] ?? [];
        $tools    = $params['tools'] ?? null;
        $stream   = $params['stream'] ?? true;

        $res = $this->request('/chat/completions', [
            'json'   => array_filter_null([
                'model'           => $model,
                'messages'        => $messages,
                'stream'          => $stream,
                'tools'           => $tools,
                'temperature'     => $params['temperature'] ?? null,
                'max_tokens'      => $params['max_tokens'] ?? null,
                'seed'            => $params['seed'] ?? null,
                'response_format' => $params['response_format'] ?? null,
            ]),
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $usage        = null;
            $finishReason = null;

            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta = $result['delta'] ?? null;
                if (empty($usage)) {
                    $usage = $result['usage'] ?? null;
                }
                if (empty($finishReason)) {
                    $finishReason = $result['finish_reason'] ?? null;
                }

                if (!empty($delta)) {
                    yield [
                        'delta'         => $delta,
                        'finish_reason' => null,
                    ];
                }
            }

            yield [
                'usage'         => $this->applyFactor($usage),
                'finish_reason' => $finishReason ?? 'stop',
            ];
        } else {
            $message      = $res['message'];
            $finishReason = $res['finish_reason'];
            $usage        = $res['usage'];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
