<?php

namespace app\lib\llm\channel\ollama;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages = $params['messages'] ?? [];
        $tools    = $params['tools'] ?? null;
        $stream   = $params['stream'] ?? true;

        $res = $this->request('/v1/chat/completions', [
            'json'   => [
                'model'           => $model,
                'messages'        => $messages,
                'stream'          => $stream,
                'stream_options'  => $stream ? [
                    'include_usage' => true,
                ] : null,
                'tools'           => $tools,
                'temperature'     => $params['temperature'] ?? null,
                'max_tokens'      => $params['max_tokens'] ?? null,
                'response_format' => $params['response_format'] ?? null,
            ],
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta = $result['choices'][0]['delta'] ?? null;
                $usage = $result['usage'] ?? null;
                if (!empty($result['choices'][0]['finish_reason'])) {
                    $finishReason = $result['choices'][0]['finish_reason'];
                }

                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($call) {
                        if ($call['index'] != $current['index']) {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = $current;
                        } else {
                            $call['function']['arguments'] .= $current['function']['arguments'];
                        }
                    } else {
                        $call = $current;
                    }
                } else {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }
                    if (!empty($delta)) {
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }
                    if (!empty($finishReason) && !empty($usage)) {
                        yield [
                            'usage'         => $this->applyFactor($usage),
                            'finish_reason' => $finishReason,
                        ];
                    }
                }
            }
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
