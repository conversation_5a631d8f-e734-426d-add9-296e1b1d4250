<?php

namespace app\lib\llm\channel\flux;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Flux';

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $content = $response->getBody()->getContents();
        $result = json_decode($content, true);

        if ($statusCode !== 200) {
            $message = 'Unknown error';
            if ($result) {
                $message = Arr::get($result, 'error.message', 
                          Arr::get($result, 'message', 
                          Arr::get($result, 'detail', $message)));
            }
            throw new Exception($message);
        }

        return $result;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.bfl.ai'),
            'headers'  => [
                'x-key' => $this->getAuth(),
                'Content-Type' => 'application/json',
                'accept' => 'application/json',
            ],
        ];
    }
}
