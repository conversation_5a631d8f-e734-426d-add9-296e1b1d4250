<?php

namespace app\lib\llm\channel\flux;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;
use think\helper\Arr;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        $baseFeatures = [
            'sizes' => [
                '1:1',     // 1024x1024
                '4:3',     // 1024x768
                '3:4',     // 768x1024
                '16:9',    // 1024x576
                '9:16',    // 576x1024
                '3:2',     // 1024x682
                '2:3',     // 682x1024
                '7:3',     // 1024x439
                '3:7',     // 439x1024
            ],
            'seed' => true,
        ];

        // 根据不同模型返回不同特性
        return match ($model) {
            'flux-kontext-pro', 'flux-kontext-max' => [
                ...$baseFeatures,
                'prompt_upsampling' => true,
                'safety_tolerance' => true,
                'output_format' => ['jpeg', 'png'],
            ],
            'flux-pro-1.1-ultra', 'flux-pro-1.1', 'flux-pro' => [
                ...$baseFeatures,
                'quality' => true,
            ],
            'flux-dev' => [
                ...$baseFeatures,
                'steps' => true,
                'guidance' => true,
            ],
            default => $baseFeatures,
        };
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? 'flux-kontext-pro';
        $prompt = $params['prompt'] ?? '';
        $aspectRatio = $params['size'] ?? '1:1';
        $format = $params['response_format'] ?? 'url';

        if ($format !== 'url') {
            throw new Exception("not support format: {$format}");
        }

        // 转换size参数为aspect_ratio
        if (str_contains($aspectRatio, 'x')) {
            $aspectRatio = $this->convertSizeToAspectRatio($aspectRatio);
        }

        // 构建请求数据，根据模型支持的参数进行过滤
        $data = array_filter_null([
            'prompt' => $prompt,
            'aspect_ratio' => $aspectRatio,
            'seed' => $params['seed'] ?? null,
        ]);

        // 根据模型添加特定参数
        if (in_array($model, ['flux-kontext-pro', 'flux-kontext-max'])) {
            $data = array_merge($data, array_filter_null([
                'prompt_upsampling' => $params['prompt_upsampling'] ?? null,
                'safety_tolerance' => $params['safety_tolerance'] ?? null,
                'output_format' => $params['output_format'] ?? null,
            ]));
        } elseif (in_array($model, ['flux-pro-1.1-ultra', 'flux-pro-1.1', 'flux-pro'])) {
            $data = array_merge($data, array_filter_null([
                'quality' => $params['quality'] ?? null,
            ]));
        } elseif ($model === 'flux-dev') {
            $data = array_merge($data, array_filter_null([
                'steps' => $params['steps'] ?? null,
                'guidance' => $params['guidance'] ?? null,
            ]));
        }

        // 提交生成请求
        $result = $this->request("/v1/{$model}", [
            'json' => $data,
        ]);

        $taskId = Arr::get($result, 'id');
        $pollingUrl = Arr::get($result, 'polling_url');

        if (empty($taskId)) {
            throw new Exception('Failed to get task ID from response');
        }

        // 轮询结果
        $finalResult = $this->pollForResult($pollingUrl ?: "/v1/get_result?id={$taskId}");

        return [
            'images' => [
                ['url' => Arr::get($finalResult, 'result.sample')]
            ],
            'usage' => $this->applyFactor([
                'prompt_tokens' => 1,
                'completion_tokens' => 0,
                'total_tokens' => 1,
            ]),
        ];
    }

    protected function convertSizeToAspectRatio($size)
    {
        $sizeMap = [
            '1024x1024' => '1:1',
            '1024x768' => '4:3',
            '768x1024' => '3:4',
            '1024x576' => '16:9',
            '576x1024' => '9:16',
            '1024x682' => '3:2',
            '682x1024' => '2:3',
            '1024x439' => '7:3',
            '439x1024' => '3:7',
        ];

        return $sizeMap[$size] ?? '1:1';
    }

    protected function pollForResult($pollingUrl, $maxWaitTime = 300)
    {
        $startTime = time();

        while (true) {
            // 如果pollingUrl是完整URL，需要特殊处理
            if (str_starts_with($pollingUrl, 'http')) {
                // 解析URL获取路径和查询参数
                $parsedUrl = parse_url($pollingUrl);
                $path = $parsedUrl['path'] ?? '';
                $query = $parsedUrl['query'] ?? '';
                $uri = $path . ($query ? '?' . $query : '');
            } else {
                $uri = $pollingUrl;
            }

            $result = $this->request($uri, [], 'GET');
            $status = Arr::get($result, 'status');

            if ($status === 'Ready') {
                return $result;
            }

            if (in_array($status, ['Error', 'Failed'])) {
                $errorMessage = Arr::get($result, 'error.message',
                               Arr::get($result, 'message', 'Generation failed'));
                throw new Exception($errorMessage);
            }

            if (time() - $startTime > $maxWaitTime) {
                throw new Exception('Generation timeout');
            }

            // 等待0.5秒后重试
            usleep(500000);
        }
    }
}
