<?php

namespace app\lib\llm\channel\hunyuan;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{
    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? 0.5;

        $messages = array_map(function ($message) {
            $msg = [
                'Role'    => $message['role'],
                'Content' => $message['content'],
            ];
            if ($msg['Role'] == 'tool') {
                $msg['ToolCallId'] = $message['tool_call_id'];
            }

            return $msg;
        }, $messages);

        if (!empty($tools)) {
            $tools = array_map(function ($item) {
                return [
                    'Type'     => $item['type'],
                    'Function' => [
                        'Name'        => $item['function']['name'],
                        'Parameters'  => !empty($item['function']['parameters']) ? json_encode($item['function']['parameters']) : null,
                        'Description' => $item['function']['description'] ?? null,
                    ],
                ];
            }, $tools);
        }

        $res = $this->request('/', [
            'headers' => [
                'X-TC-Action'  => 'ChatCompletions',
                'X-TC-Version' => '2023-09-01',
            ],
            'json'    => [
                'Model'       => $model,
                'Messages'    => $messages,
                'Stream'      => $stream,
                'Temperature' => $temperature * 2,
                'Tools'       => $tools,
            ],
            'stream'  => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }
                $result = json_decode($data, true);

                $choice       = $result['Choices'][0];
                $delta        = $choice['Delta'];
                $finishReason = $choice['FinishReason'] ?? null;

                if (!empty($delta['ToolCalls'])) {
                    $current = $delta['ToolCalls'][0];
                    if ($current['Type'] == 'function') {
                        if (!isset($current['Index'])) {
                            $current['Index'] = 0;
                        }
                        if ($call) {
                            if ($call['Index'] != $current['Index']) {
                                yield [
                                    'delta'         => [
                                        'role'       => 'assistant',
                                        'content'    => null,
                                        'tool_calls' => [
                                            [
                                                'id'       => $call['Id'],
                                                'index'    => $call['Index'],
                                                'type'     => $call['Type'],
                                                'function' => [
                                                    'name'      => $call['Function']['Name'],
                                                    'arguments' => $call['Function']['Arguments'],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'finish_reason' => null,
                                ];
                                $call = $current;
                            } else {
                                $call['Function']['Arguments'] .= $current['Function']['Arguments'];
                            }
                        } else {
                            $call = $current;
                        }
                    } else {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [
                                    [
                                        'id'       => $current['Id'],
                                        'index'    => $current['Index'],
                                        'type'     => $current['Type'],
                                        'function' => [
                                            'name'      => $current['Function']['Name'],
                                            'arguments' => $current['Function']['Arguments'],
                                        ],
                                    ],
                                ],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                } else {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [
                                    [
                                        'id'       => $call['Id'],
                                        'index'    => $call['Index'],
                                        'type'     => $call['Type'],
                                        'function' => [
                                            'name'      => $call['Function']['Name'],
                                            'arguments' => $call['Function']['Arguments'],
                                        ],
                                    ],
                                ],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }

                    if (strlen($delta['Content'] ?? '') > 0) {
                        yield [
                            'delta'         => [
                                'role'    => $delta['Role'],
                                'content' => $delta['Content'],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                }

                if (!empty($finishReason)) {
                    $usage = [
                        'prompt_tokens'     => $result['Usage']['PromptTokens'],
                        'completion_tokens' => $result['Usage']['CompletionTokens'],
                        'total_tokens'      => $result['Usage']['TotalTokens'],
                    ];
                    yield [
                        'usage'         => $this->applyFactor($usage),
                        'finish_reason' => $finishReason,
                    ];
                }
            }
        } else {
            $choice  = $res['Choices'][0];
            $message = [
                'role'    => $choice['Message']['Role'],
                'content' => $choice['Message']['Content'],
            ];

            if (!empty($choice['Message']['ToolCalls'])) {
                $message['tool_calls'] = array_map(function ($current) {
                    return [
                        'id'       => $current['Id'],
                        'index'    => $current['Index'],
                        'type'     => $current['Type'],
                        'function' => [
                            'name'      => $current['Function']['Name'],
                            'arguments' => $current['Function']['Arguments'],
                        ],
                    ];
                }, $choice['Message']['ToolCalls']);
            }

            $finishReason = $choice['FinishReason'];

            $usage = [
                'prompt_tokens'     => $res['Usage']['PromptTokens'],
                'completion_tokens' => $res['Usage']['CompletionTokens'],
                'total_tokens'      => $res['Usage']['TotalTokens'],
            ];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
