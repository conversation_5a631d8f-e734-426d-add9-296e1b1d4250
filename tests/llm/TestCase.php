<?php

namespace tests\llm;

use app\lib\Llm;
use Mo<PERSON>y;
use think\helper\Str;

abstract class TestCase extends \tests\TestCase
{
    protected Llm $llm;

    protected static $providers = [];
    protected static $cases     = [];
    protected static $models    = [];

    protected function getChannel()
    {
        $ref       = new \ReflectionClass($this);
        $namespace = $ref->getNamespaceName();

        return basename(str_replace('\\', '/', $namespace));
    }

    protected function getOptions($type, $code)
    {
        return [
            'type'       => $type,
            'code'       => $code,
            'checkpoint' => $code,
            'factor'     => 1,
            'params'     => static::$models[$code] ?? [],
            'auth'       => $this->getAuth(),
            'base_uri'   => $this->getBaseUri(),
        ];
    }

    protected function getBaseUri()
    {
        return '';
    }

    protected function getAuth()
    {
        return '';
    }

    public function setUp(): void
    {
        // Create a partial mock that allows all methods to pass through except getDriver
        $this->llm = Mockery::mock(Llm::class)->makePartial();

        $this->llm->shouldReceive('getDriver')->andReturnUsing(function ($type, $code, $method) {
            $channel = $this->getChannel();

            $class   = "\\app\\lib\\llm\\channel\\{$channel}\\" . Str::studly($type);
            $options = $this->getOptions($type, $code);

            $driver = new $class($options);

            return function ($params) use ($method, $driver) {
                return call_user_func([$driver, $method], $params);
            };
        });

        if ($this->usesDataProvider()) {
            $data = $this->providedData();

            if (empty($data)) {
                $this->markTestSkipped();
            }
        }
    }
}
