<?php

namespace llm\anthropic;

class ChatTest extends \tests\llm\ChatTest
{
    protected static $cases = [
        'base'      => ['claude-3-5-sonnet-latest'],
        'tools'     => ['claude-3-7-sonnet-latest', 'claude-3-5-sonnet-latest'],
        'reasoning' => ['claude-3-7-sonnet-20250219'],
    ];

    protected static $models = [
        'claude-3-7-sonnet-20250219' => [
            'reasoning' => false,
        ],
    ];

    protected function getAuth()
    {
        return env('ANTHROPIC_KEY');
    }

    protected function getBaseUri()
    {
        return env('ANTHROPIC_BASE_URI');
    }

    public function testNoneParametersTools($model = 'claude-3-7-sonnet-20250219', $stream = true)
    {
        $tools = [
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'get_ip',
                    'description' => '获取当前IP',
                ],
            ],
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'get_address',
                    'description' => '根据IP获取地址',
                    'parameters'  => [
                        'type'       => 'object',
                        'properties' => [
                            'ip' => [
                                'type' => 'string',
                            ],
                        ],
                        'required'   => ['ip'],
                    ],
                ],
            ],
        ];

        $messages = [
            [
                'role'    => 'user',
                'content' => '今天天气怎么样？',
            ],
            [
                'role'       => 'assistant',
                'content'    => '我需要获得你所在地的信息才能查询天气情况，现在获取IP地址',
                'tool_calls' => [
                    [
                        'id'       => 'call_0',
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'get_ip',
                            'arguments' => '',
                        ],
                    ],
                ],
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => 'call_0',
                'content'      => '************',
            ],
            [
                'role'       => 'assistant',
                'content'    => '现在根据IP查询所在地',
                'tool_calls' => [
                    [
                        'id'       => 'call_1',
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'get_address',
                            'arguments' => '{"ip": "************"}',
                        ],
                    ],
                ],
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => 'call_1',
                'content'      => '上海',
            ],
        ];

        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => $messages,
            'tools'    => $tools,
            'stream'   => $stream,
        ]);

        $result->rewind();

        $tokens = 0;

        foreach ($result as $event) {
            $this->assertArrayHasKeys('finish_reason', $event);
            $reason = $event['finish_reason'];
            if ($reason === 'stop' || $reason === 'tool_calls') {
                $this->assertArrayHasKeys(['usage.total_tokens'], $event);
                $tokens = $event['usage']['total_tokens'];
            } else {
                $this->assertArrayHasKeys(['delta'], $event);
                $this->assertArrayNotHasKeys(['delta.tool_calls'], $event);
            }
        }
        $this->assertGreaterThan(0, $tokens);
    }

    public function testNoneParametersTools2($model = 'claude-3-7-sonnet-20250219', $stream = true)
    {
        $tools = [
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'get_time',
                    'description' => '获取当前时间',
                ],
            ],
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'get_address',
                    'description' => '获取地址',
                ],
            ],
        ];

        $messages = [
            [
                'role'    => 'user',
                'content' => '现在天气怎么样？',
            ],
            [
                'role'       => 'assistant',
                'content'    => '我需要获得你的时间和地址',
                'tool_calls' => [
                    [
                        'id'       => 'call_0',
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'get_time',
                            'arguments' => '',
                        ],
                    ],
                    [
                        'id'       => 'call_1',
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'get_address',
                            'arguments' => '',
                        ],
                    ],
                ],
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => 'call_0',
                'content'      => '2025/2/26 22:40',
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => 'call_1',
                'content'      => '上海',
            ],
        ];

        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => $messages,
            'tools'    => $tools,
            'stream'   => $stream,
        ]);

        $result->rewind();

        $tokens = 0;

        foreach ($result as $event) {
            $this->assertArrayHasKeys('finish_reason', $event);
            $reason = $event['finish_reason'];
            if ($reason === 'stop' || $reason === 'tool_calls') {
                $this->assertArrayHasKeys(['usage.total_tokens'], $event);
                $tokens = $event['usage']['total_tokens'];
            } else {
                $this->assertArrayHasKeys(['delta'], $event);
                $this->assertArrayNotHasKeys(['delta.tool_calls'], $event);
            }
        }
        $this->assertGreaterThan(0, $tokens);
    }

    public function testToolsParameters($model = 'claude-3-7-sonnet-20250219', $stream = true)
    {
        $tools = [
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'query_weather',
                    'description' => '查询某个城市的天气',
                    'parameters'  => [
                        'type'       => 'object',
                        'properties' => [
                            'city' => [
                                'type' => 'string',
                            ],
                        ],
                        'required'   => ['city'],
                    ],
                ],
            ],
        ];

        $messages = [
            [
                'role'    => 'user',
                'content' => '上海天气怎么样？',
            ],
        ];

        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => $messages,
            'tools'    => $tools,
            'stream'   => $stream,
        ]);

        $result->rewind();

        $tokens = 0;
        $calls  = [];

        foreach ($result as $event) {
            $this->assertArrayHasKeys('finish_reason', $event);
            $reason = $event['finish_reason'];
            if ($reason === 'stop' || $reason === 'tool_calls') {
                $this->assertArrayHasKeys(['usage.total_tokens'], $event);
                $tokens = $event['usage']['total_tokens'];
            } else {
                $this->assertArrayHasKeys(['delta'], $event);
                if (!empty($event['delta']['tool_calls'])) {
                    $calls += $event['delta']['tool_calls'];
                }
            }
        }
        $this->assertGreaterThan(0, $tokens);
        $this->assertGreaterThan(0, count($calls));
    }
}
